import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { Providers } from "@/components/providers"
import AuthInitializer from "@/providers/AuthInitializer"
import { AppShell } from "@/components/layout/AppShell"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Togeda.ai - Plan Your Next Adventure",
  description: "Plan and organize trips with your friends",
  icons: {
    icon: [
      { url: "/togeda_favicon.svg", type: "image/svg+xml" },
      { url: "/togeda_favicon.ico", type: "image/x-icon" },
    ],
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    title: "Togeda.ai - Plan Your Next Adventure",
    statusBarStyle: "default",
  },
}

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <AuthInitializer />
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
